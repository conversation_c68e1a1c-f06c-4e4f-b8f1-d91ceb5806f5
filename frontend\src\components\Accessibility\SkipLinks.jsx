import React from 'react'
import { cn } from '@/lib/utils'

const SkipLinks = ({ links = [] }) => {
  const defaultLinks = [
    { href: '#main-content', label: 'Skip to main content' },
    { href: '#navigation', label: 'Skip to navigation' },
    { href: '#search', label: 'Skip to search' },
  ]

  const allLinks = [...defaultLinks, ...links]

  const handleSkipClick = (e, href) => {
    e.preventDefault()
    const target = document.querySelector(href)
    if (target) {
      target.focus()
      target.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  return (
    <div className="skip-links">
      {allLinks.map((link, index) => (
        <a
          key={index}
          href={link.href}
          onClick={(e) => handleSkipClick(e, link.href)}
          className={cn(
            "absolute left-0 top-0 z-[9999] px-4 py-2 bg-primary text-primary-foreground",
            "font-medium text-sm rounded-br-md shadow-lg",
            "transform -translate-y-full opacity-0 pointer-events-none",
            "focus:translate-y-0 focus:opacity-100 focus:pointer-events-auto",
            "transition-all duration-200 ease-in-out",
            "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          )}
          aria-label={link.label}
        >
          {link.label}
        </a>
      ))}
    </div>
  )
}

export default SkipLinks
