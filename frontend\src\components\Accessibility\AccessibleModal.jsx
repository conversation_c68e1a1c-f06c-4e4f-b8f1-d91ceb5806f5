import React, { useEffect, useRef } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog'
import { useFocusManagement, useAnnouncements } from '../../hooks/useAccessibility'
import { X } from 'lucide-react'
import { Button } from '../ui/button'
import { cn } from '@/lib/utils'

/**
 * Accessible Modal component with focus management and ARIA support
 */
const AccessibleModal = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  className,
  closeOnEscape = true,
  closeOnOverlayClick = true,
  initialFocus,
  restoreFocus = true,
  announceOnOpen = true,
  ...props
}) => {
  const { trapFocus, restoreFocus: restoreFocusUtil } = useFocusManagement()
  const { announce } = useAnnouncements()
  const previousFocusRef = useRef(null)
  const modalRef = useRef(null)

  // Store the previously focused element
  useEffect(() => {
    if (isOpen) {
      previousFocusRef.current = document.activeElement
      
      if (announceOnOpen && title) {
        announce(`Dialog opened: ${title}`)
      }
    }
  }, [isOpen, title, announceOnOpen, announce])

  // Focus management
  useEffect(() => {
    if (isOpen && modalRef.current) {
      const cleanup = trapFocus(modalRef.current)
      
      // Focus initial element or first focusable element
      if (initialFocus) {
        const target = typeof initialFocus === 'string' 
          ? modalRef.current.querySelector(initialFocus)
          : initialFocus.current
        target?.focus()
      }

      return cleanup
    }
  }, [isOpen, trapFocus, initialFocus])

  // Restore focus when modal closes
  useEffect(() => {
    if (!isOpen && restoreFocus && previousFocusRef.current) {
      restoreFocusUtil(previousFocusRef.current)
    }
  }, [isOpen, restoreFocus, restoreFocusUtil])

  // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return

    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        e.preventDefault()
        onClose?.()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, closeOnEscape, onClose])

  const handleOverlayClick = (e) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose?.()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose} {...props}>
      <DialogContent
        ref={modalRef}
        className={cn(
          // Enhanced focus styles
          "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          // High contrast support
          "contrast-more:border-2 contrast-more:border-current",
          className
        )}
        role="dialog"
        aria-modal="true"
        aria-labelledby={title ? "modal-title" : undefined}
        aria-describedby={description ? "modal-description" : undefined}
        onClick={handleOverlayClick}
      >
        {/* Close button */}
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          onClick={onClose}
          aria-label="Close dialog"
        >
          <X className="h-4 w-4" />
        </Button>

        {/* Header */}
        {(title || description) && (
          <DialogHeader>
            {title && (
              <DialogTitle 
                id="modal-title"
                className="text-lg font-semibold leading-none tracking-tight"
              >
                {title}
              </DialogTitle>
            )}
            {description && (
              <DialogDescription 
                id="modal-description"
                className="text-sm text-muted-foreground"
              >
                {description}
              </DialogDescription>
            )}
          </DialogHeader>
        )}

        {/* Content */}
        <div className="modal-content">
          {children}
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default AccessibleModal
