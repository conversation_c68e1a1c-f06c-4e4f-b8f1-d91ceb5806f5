import React from 'react'
import { Alert } from '@/components/ui/alert'
import PhoneNumberManager from '../components/PhoneNumbers/PhoneNumberManager'
import { useWhatsAppStore } from '../stores/whatsappStore'

const PhoneNumbersPage = () => {
  const { isAuthenticated, isReady } = useWhatsAppStore()

  if (!isAuthenticated) {
    return (
      <div className="space-y-6 p-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Phone Numbers</h1>
          <p className="text-muted-foreground">
            Manage your WhatsApp phone numbers and contacts
          </p>
        </div>
        
        <Alert className="border-yellow-200 bg-yellow-50 text-yellow-800">
          WhatsApp is not authenticated. Please scan the QR code on the dashboard first.
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Phone Numbers</h1>
        <p className="text-muted-foreground">
          Manage your WhatsApp phone numbers and contacts
        </p>
      </div>

      {/* Status Alert */}
      {!isReady && (
        <Alert className="border-yellow-200 bg-yellow-50 text-yellow-800">
          WhatsApp client is not ready. Some features may be limited until connection is established.
        </Alert>
      )}

      {/* Phone Number Manager */}
      <PhoneNumberManager />
    </div>
  )
}

export default PhoneNumbersPage
