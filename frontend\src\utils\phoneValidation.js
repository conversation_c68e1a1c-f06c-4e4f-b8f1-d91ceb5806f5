/**
 * Phone number validation and formatting utilities
 */

// Country codes and their phone number patterns
const COUNTRY_PATTERNS = {
  US: {
    code: '+1',
    pattern: /^1?[2-9]\d{2}[2-9]\d{2}\d{4}$/,
    format: '(XXX) XXX-XXXX',
    length: [10, 11],
    example: '+****************'
  },
  CA: {
    code: '+1',
    pattern: /^1?[2-9]\d{2}[2-9]\d{2}\d{4}$/,
    format: '(XXX) XXX-XXXX',
    length: [10, 11],
    example: '+****************'
  },
  GB: {
    code: '+44',
    pattern: /^44?[1-9]\d{8,9}$/,
    format: 'XXXX XXX XXXX',
    length: [10, 11],
    example: '+44 7700 900123'
  },
  IN: {
    code: '+91',
    pattern: /^91?[6-9]\d{9}$/,
    format: 'XXXXX XXXXX',
    length: [10, 12],
    example: '+91 98765 43210'
  },
  BR: {
    code: '+55',
    pattern: /^55?[1-9]{2}9?[6-9]\d{7}$/,
    format: '(XX) XXXXX-XXXX',
    length: [10, 13],
    example: '+55 (11) 99999-9999'
  },
  // Add more countries as needed
}

/**
 * Clean phone number by removing all non-digit characters
 * @param {string} phoneNumber - Raw phone number input
 * @returns {string} - Cleaned phone number with only digits
 */
export const cleanPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return ''
  return phoneNumber.replace(/\D/g, '')
}

/**
 * Detect country from phone number
 * @param {string} phoneNumber - Phone number to analyze
 * @returns {string|null} - Country code or null if not detected
 */
export const detectCountry = (phoneNumber) => {
  const cleaned = cleanPhoneNumber(phoneNumber)
  
  for (const [country, config] of Object.entries(COUNTRY_PATTERNS)) {
    if (config.pattern.test(cleaned)) {
      return country
    }
  }
  
  return null
}

/**
 * Validate phone number format
 * @param {string} phoneNumber - Phone number to validate
 * @param {string} country - Optional country code to validate against
 * @returns {object} - Validation result with isValid, country, formatted, and errors
 */
export const validatePhoneNumber = (phoneNumber, country = null) => {
  const result = {
    isValid: false,
    country: null,
    formatted: null,
    chatId: null,
    errors: []
  }

  if (!phoneNumber) {
    result.errors.push('Phone number is required')
    return result
  }

  const cleaned = cleanPhoneNumber(phoneNumber)
  
  if (cleaned.length < 7) {
    result.errors.push('Phone number is too short')
    return result
  }

  if (cleaned.length > 15) {
    result.errors.push('Phone number is too long')
    return result
  }

  // If country is specified, validate against that country's pattern
  if (country && COUNTRY_PATTERNS[country]) {
    const config = COUNTRY_PATTERNS[country]
    if (config.pattern.test(cleaned)) {
      result.isValid = true
      result.country = country
      result.formatted = formatPhoneNumber(cleaned, country)
      result.chatId = `${cleaned}@c.us`
    } else {
      result.errors.push(`Invalid ${country} phone number format`)
    }
  } else {
    // Auto-detect country
    const detectedCountry = detectCountry(cleaned)
    if (detectedCountry) {
      result.isValid = true
      result.country = detectedCountry
      result.formatted = formatPhoneNumber(cleaned, detectedCountry)
      result.chatId = `${cleaned}@c.us`
    } else {
      // Basic validation for international numbers
      if (cleaned.length >= 7 && cleaned.length <= 15) {
        result.isValid = true
        result.country = 'UNKNOWN'
        result.formatted = `+${cleaned}`
        result.chatId = `${cleaned}@c.us`
      } else {
        result.errors.push('Invalid phone number format')
      }
    }
  }

  return result
}

/**
 * Format phone number according to country standards
 * @param {string} phoneNumber - Clean phone number (digits only)
 * @param {string} country - Country code
 * @returns {string} - Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber, country) => {
  const cleaned = cleanPhoneNumber(phoneNumber)
  const config = COUNTRY_PATTERNS[country]
  
  if (!config) {
    return `+${cleaned}`
  }

  switch (country) {
    case 'US':
    case 'CA':
      // Remove leading 1 if present for formatting
      const usNumber = cleaned.startsWith('1') ? cleaned.slice(1) : cleaned
      if (usNumber.length === 10) {
        return `+1 (${usNumber.slice(0, 3)}) ${usNumber.slice(3, 6)}-${usNumber.slice(6)}`
      }
      break
    case 'GB':
      if (cleaned.startsWith('44')) {
        const gbNumber = cleaned.slice(2)
        return `+44 ${gbNumber.slice(0, 4)} ${gbNumber.slice(4, 7)} ${gbNumber.slice(7)}`
      }
      break
    case 'IN':
      if (cleaned.startsWith('91')) {
        const inNumber = cleaned.slice(2)
        return `+91 ${inNumber.slice(0, 5)} ${inNumber.slice(5)}`
      }
      break
    case 'BR':
      if (cleaned.startsWith('55')) {
        const brNumber = cleaned.slice(2)
        return `+55 (${brNumber.slice(0, 2)}) ${brNumber.slice(2, 7)}-${brNumber.slice(7)}`
      }
      break
  }
  
  return `+${cleaned}`
}

/**
 * Convert phone number to WhatsApp chat ID format
 * @param {string} phoneNumber - Phone number to convert
 * @returns {string} - WhatsApp chat ID
 */
export const toWhatsAppChatId = (phoneNumber) => {
  const cleaned = cleanPhoneNumber(phoneNumber)
  return `${cleaned}@c.us`
}

/**
 * Extract phone number from WhatsApp chat ID
 * @param {string} chatId - WhatsApp chat ID
 * @returns {string} - Phone number
 */
export const fromWhatsAppChatId = (chatId) => {
  return chatId.split('@')[0]
}

/**
 * Check if a string is a valid WhatsApp chat ID
 * @param {string} chatId - String to check
 * @returns {boolean} - True if valid chat ID
 */
export const isValidChatId = (chatId) => {
  const chatIdRegex = /^\d+@c\.us$/
  return chatIdRegex.test(chatId)
}

/**
 * Get country list for dropdown
 * @returns {Array} - Array of country objects
 */
export const getCountryList = () => {
  return Object.entries(COUNTRY_PATTERNS).map(([code, config]) => ({
    code,
    name: getCountryName(code),
    dialCode: config.code,
    example: config.example,
    format: config.format
  }))
}

/**
 * Get country name from code
 * @param {string} code - Country code
 * @returns {string} - Country name
 */
const getCountryName = (code) => {
  const names = {
    US: 'United States',
    CA: 'Canada',
    GB: 'United Kingdom',
    IN: 'India',
    BR: 'Brazil'
  }
  return names[code] || code
}

/**
 * Normalize phone number for storage/comparison
 * @param {string} phoneNumber - Phone number to normalize
 * @returns {string} - Normalized phone number
 */
export const normalizePhoneNumber = (phoneNumber) => {
  const cleaned = cleanPhoneNumber(phoneNumber)
  
  // Add country code if missing (default to US)
  if (cleaned.length === 10 && !cleaned.startsWith('1')) {
    return `1${cleaned}`
  }
  
  return cleaned
}
