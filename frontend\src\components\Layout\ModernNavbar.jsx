import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import {
  Search,
  Bell,
  Menu,
  User,
  Settings,
  LogOut,
  Moon,
  Sun,
  Wifi,
  WifiOff,
} from 'lucide-react'

import { useAuthStore } from '../../stores/authStore'
import { useWhatsAppStore } from '../../stores/whatsappStore'
import { useSocketStore } from '../../stores/socketStore'
import ModernSidebar from './ModernSidebar'

const ModernNavbar = ({ onToggleSidebar, sidebarCollapsed }) => {
  const { user, logout } = useAuthStore()
  const { isReady, isAuthenticated, connectionState } = useWhatsAppStore()
  const { isConnected } = useSocketStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [darkMode, setDarkMode] = useState(false)

  const handleLogout = () => {
    logout()
  }

  const toggleDarkMode = () => {
    setDarkMode(!darkMode)
    document.documentElement.classList.toggle('dark')
  }

  const getStatusColor = () => {
    if (!isConnected) return 'destructive'
    if (isReady && isAuthenticated) return 'default'
    if (isAuthenticated) return 'secondary'
    return 'outline'
  }

  const getStatusText = () => {
    if (!isConnected) return 'Disconnected'
    if (isReady && isAuthenticated) return 'Ready'
    if (isAuthenticated) return 'Connecting'
    return 'Not Authenticated'
  }

  const getStatusIcon = () => {
    return isConnected ? Wifi : WifiOff
  }

  const StatusIcon = getStatusIcon()

  return (
    <header
      className="sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
      role="banner"
      aria-label="Main navigation header"
    >
      <div className="flex h-16 items-center justify-between px-4">
        {/* Left Section */}
        <div className="flex items-center gap-4">
          {/* Mobile Menu */}
          <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                aria-label="Open navigation menu"
                aria-expanded="false"
                aria-controls="mobile-navigation"
              >
                <Menu className="h-5 w-5" aria-hidden="true" />
              </Button>
            </SheetTrigger>
            <SheetContent
              side="left"
              className="p-0 w-64"
              id="mobile-navigation"
              aria-label="Mobile navigation menu"
            >
              <ModernSidebar />
            </SheetContent>
          </Sheet>

          {/* Desktop Sidebar Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleSidebar}
            className="hidden md:flex focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            aria-label={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            aria-expanded={!sidebarCollapsed}
            aria-controls="navigation"
          >
            <Menu className="h-5 w-5" aria-hidden="true" />
          </Button>

          {/* Search */}
          <div className="relative hidden sm:block">
            <label htmlFor="global-search" className="sr-only">
              Search the application
            </label>
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
              aria-hidden="true"
            />
            <Input
              id="global-search"
              type="search"
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64 bg-muted/50 border-0 focus-visible:ring-1 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              aria-label="Search the application"
              role="searchbox"
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-2">
          {/* WhatsApp Status */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="flex items-center gap-2 px-3 py-1.5 rounded-md bg-muted/50"
                  role="status"
                  aria-label={`WhatsApp status: ${getStatusText()}, Connection: ${connectionState}`}
                >
                  <StatusIcon
                    className={cn(
                      "h-4 w-4",
                      isConnected ? "text-green-500" : "text-red-500"
                    )}
                    aria-hidden="true"
                  />
                  <Badge variant={getStatusColor()} className="text-xs">
                    {getStatusText()}
                  </Badge>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>WhatsApp Status: {getStatusText()}</p>
                <p className="text-xs text-muted-foreground">
                  Connection: {connectionState}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator orientation="vertical" className="h-6" />

          {/* Dark Mode Toggle */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleDarkMode}
                  className="focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  aria-label={`Switch to ${darkMode ? 'light' : 'dark'} mode`}
                  aria-pressed={darkMode}
                >
                  {darkMode ? (
                    <Sun className="h-4 w-4" aria-hidden="true" />
                  ) : (
                    <Moon className="h-4 w-4" aria-hidden="true" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle {darkMode ? 'light' : 'dark'} mode</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Notifications */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="relative focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  aria-label="Notifications (1 unread)"
                  aria-describedby="notification-count"
                >
                  <Bell className="h-4 w-4" aria-hidden="true" />
                  <span
                    id="notification-count"
                    className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs"
                    aria-label="1 unread notification"
                  ></span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Notifications (1 unread)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                <Avatar className="h-9 w-9">
                  <div className="flex h-full w-full items-center justify-center bg-primary text-primary-foreground text-sm font-medium">
                    {user?.username?.charAt(0).toUpperCase() || 'U'}
                  </div>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user?.username || 'User'}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.email || '<EMAIL>'}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}

export default ModernNavbar
