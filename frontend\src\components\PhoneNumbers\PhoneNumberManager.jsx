import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Badge } from '../ui/badge'
import { Alert, AlertDescription } from '../ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog'
import { 
  Plus, 
  Phone, 
  Edit, 
  Trash2, 
  Check, 
  X, 
  Search,
  Download,
  Upload,
  AlertCircle,
  CheckCircle2,
  Loader2
} from 'lucide-react'

import { 
  validatePhoneNumber, 
  formatPhone<PERSON><PERSON>ber, 
  getCountryList,
  normalizePhoneNumber,
  toWhatsAppChatId
} from '../../utils/phoneValidation'
import { contactsAPI } from '../../services/api'

const PhoneNumberManager = ({ className }) => {
  const [phoneNumbers, setPhoneNumbers] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingNumber, setEditingNumber] = useState(null)
  const [validationResults, setValidationResults] = useState({})

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors }
  } = useForm()

  const watchedPhone = watch('phoneNumber', '')
  const watchedCountry = watch('country', 'US')

  // Real-time validation
  useEffect(() => {
    if (watchedPhone) {
      const result = validatePhoneNumber(watchedPhone, watchedCountry)
      setValidationResults(result)
    } else {
      setValidationResults({})
    }
  }, [watchedPhone, watchedCountry])

  // Load saved phone numbers from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('whatsapp_phone_numbers')
    if (saved) {
      try {
        setPhoneNumbers(JSON.parse(saved))
      } catch (error) {
        console.error('Error loading saved phone numbers:', error)
      }
    }
  }, [])

  // Save phone numbers to localStorage
  const savePhoneNumbers = (numbers) => {
    localStorage.setItem('whatsapp_phone_numbers', JSON.stringify(numbers))
    setPhoneNumbers(numbers)
  }

  // Check if phone number is registered on WhatsApp
  const checkWhatsAppRegistration = async (phoneNumber) => {
    try {
      const response = await contactsAPI.check(phoneNumber)
      return response.data
    } catch (error) {
      console.error('Error checking WhatsApp registration:', error)
      return { isRegistered: false, error: error.message }
    }
  }

  // Add new phone number
  const onAddPhoneNumber = async (data) => {
    setIsLoading(true)
    
    try {
      const validation = validatePhoneNumber(data.phoneNumber, data.country)
      
      if (!validation.isValid) {
        alert('Please enter a valid phone number')
        return
      }

      const normalized = normalizePhoneNumber(data.phoneNumber)
      
      // Check if number already exists
      if (phoneNumbers.some(num => num.normalized === normalized)) {
        alert('This phone number already exists')
        return
      }

      // Check WhatsApp registration
      const whatsappCheck = await checkWhatsAppRegistration(normalized)

      const newNumber = {
        id: Date.now().toString(),
        original: data.phoneNumber,
        normalized,
        formatted: validation.formatted,
        country: validation.country,
        chatId: validation.chatId,
        label: data.label || '',
        notes: data.notes || '',
        isWhatsAppRegistered: whatsappCheck.isRegistered,
        whatsappContact: whatsappCheck.contact || null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      const updatedNumbers = [...phoneNumbers, newNumber]
      savePhoneNumbers(updatedNumbers)
      
      reset()
      setIsAddDialogOpen(false)
      setValidationResults({})
    } catch (error) {
      console.error('Error adding phone number:', error)
      alert('Failed to add phone number')
    } finally {
      setIsLoading(false)
    }
  }

  // Edit phone number
  const onEditPhoneNumber = async (data) => {
    setIsLoading(true)
    
    try {
      const validation = validatePhoneNumber(data.phoneNumber, data.country)
      
      if (!validation.isValid) {
        alert('Please enter a valid phone number')
        return
      }

      const normalized = normalizePhoneNumber(data.phoneNumber)
      
      // Check if number already exists (excluding current)
      if (phoneNumbers.some(num => num.normalized === normalized && num.id !== editingNumber.id)) {
        alert('This phone number already exists')
        return
      }

      // Check WhatsApp registration if number changed
      let whatsappCheck = { isRegistered: editingNumber.isWhatsAppRegistered }
      if (normalized !== editingNumber.normalized) {
        whatsappCheck = await checkWhatsAppRegistration(normalized)
      }

      const updatedNumber = {
        ...editingNumber,
        original: data.phoneNumber,
        normalized,
        formatted: validation.formatted,
        country: validation.country,
        chatId: validation.chatId,
        label: data.label || '',
        notes: data.notes || '',
        isWhatsAppRegistered: whatsappCheck.isRegistered,
        whatsappContact: whatsappCheck.contact || editingNumber.whatsappContact,
        updatedAt: new Date().toISOString()
      }

      const updatedNumbers = phoneNumbers.map(num => 
        num.id === editingNumber.id ? updatedNumber : num
      )
      savePhoneNumbers(updatedNumbers)
      
      reset()
      setEditingNumber(null)
      setValidationResults({})
    } catch (error) {
      console.error('Error updating phone number:', error)
      alert('Failed to update phone number')
    } finally {
      setIsLoading(false)
    }
  }

  // Delete phone number
  const deletePhoneNumber = (id) => {
    if (confirm('Are you sure you want to delete this phone number?')) {
      const updatedNumbers = phoneNumbers.filter(num => num.id !== id)
      savePhoneNumbers(updatedNumbers)
    }
  }

  // Refresh WhatsApp status for all numbers
  const refreshWhatsAppStatus = async () => {
    setIsLoading(true)
    
    try {
      const updatedNumbers = await Promise.all(
        phoneNumbers.map(async (number) => {
          try {
            const whatsappCheck = await checkWhatsAppRegistration(number.normalized)
            return {
              ...number,
              isWhatsAppRegistered: whatsappCheck.isRegistered,
              whatsappContact: whatsappCheck.contact || null,
              updatedAt: new Date().toISOString()
            }
          } catch (error) {
            console.error(`Error checking ${number.formatted}:`, error)
            return number
          }
        })
      )
      
      savePhoneNumbers(updatedNumbers)
    } catch (error) {
      console.error('Error refreshing WhatsApp status:', error)
      alert('Failed to refresh WhatsApp status')
    } finally {
      setIsLoading(false)
    }
  }

  // Export phone numbers
  const exportPhoneNumbers = () => {
    const csvContent = [
      ['Label', 'Phone Number', 'Country', 'WhatsApp Registered', 'Notes', 'Created'].join(','),
      ...phoneNumbers.map(num => [
        num.label || '',
        num.formatted,
        num.country,
        num.isWhatsAppRegistered ? 'Yes' : 'No',
        `"${(num.notes || '').replace(/"/g, '""')}"`,
        new Date(num.createdAt).toLocaleDateString()
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `phone-numbers-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
  }

  // Filter phone numbers
  const filteredNumbers = phoneNumbers.filter(number =>
    number.formatted.toLowerCase().includes(searchTerm.toLowerCase()) ||
    number.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    number.notes.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const countryOptions = getCountryList()

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Phone Number Management ({phoneNumbers.length})
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={refreshWhatsAppStatus} disabled={isLoading}>
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <CheckCircle2 className="h-4 w-4" />}
              Refresh Status
            </Button>
            <Button variant="outline" size="sm" onClick={exportPhoneNumbers}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Number
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Add Phone Number</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleSubmit(onAddPhoneNumber)} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="country">Country</Label>
                    <Select value={watchedCountry} onValueChange={(value) => setValue('country', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select country" />
                      </SelectTrigger>
                      <SelectContent>
                        {countryOptions.map((country) => (
                          <SelectItem key={country.code} value={country.code}>
                            {country.dialCode} {country.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber">Phone Number</Label>
                    <Input
                      id="phoneNumber"
                      placeholder="Enter phone number"
                      {...register('phoneNumber', { required: 'Phone number is required' })}
                    />
                    {errors.phoneNumber && (
                      <p className="text-sm text-red-600">{errors.phoneNumber.message}</p>
                    )}
                    
                    {/* Real-time validation feedback */}
                    {watchedPhone && validationResults.errors?.length > 0 && (
                      <Alert className="border-red-200 bg-red-50">
                        <AlertCircle className="h-4 w-4 text-red-600" />
                        <AlertDescription className="text-red-800">
                          {validationResults.errors.join(', ')}
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    {watchedPhone && validationResults.isValid && (
                      <Alert className="border-green-200 bg-green-50">
                        <CheckCircle2 className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-green-800">
                          Valid format: {validationResults.formatted}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="label">Label (Optional)</Label>
                    <Input
                      id="label"
                      placeholder="e.g., Customer Service, John Doe"
                      {...register('label')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes (Optional)</Label>
                    <Input
                      id="notes"
                      placeholder="Additional notes"
                      {...register('notes')}
                    />
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isLoading || !validationResults.isValid}>
                      {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                      Add Number
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search phone numbers, labels, or notes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Phone Numbers Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Phone Number</TableHead>
                <TableHead>Label</TableHead>
                <TableHead>Country</TableHead>
                <TableHead>WhatsApp</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredNumbers.length > 0 ? (
                filteredNumbers.map((number) => (
                  <TableRow key={number.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{number.formatted}</div>
                        <div className="text-xs text-muted-foreground">
                          Chat ID: {number.chatId}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{number.label || '-'}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{number.country}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={number.isWhatsAppRegistered ? "default" : "secondary"}>
                        {number.isWhatsAppRegistered ? (
                          <>
                            <CheckCircle2 className="h-3 w-3 mr-1" />
                            Registered
                          </>
                        ) : (
                          <>
                            <X className="h-3 w-3 mr-1" />
                            Not Found
                          </>
                        )}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate text-sm">
                        {number.notes || '-'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditingNumber(number)
                            setValue('phoneNumber', number.original)
                            setValue('country', number.country)
                            setValue('label', number.label)
                            setValue('notes', number.notes)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deletePhoneNumber(number.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex flex-col items-center gap-2">
                      <Phone className="h-12 w-12 text-muted-foreground opacity-50" />
                      <p className="text-muted-foreground">
                        {searchTerm ? 'No phone numbers match your search' : 'No phone numbers added yet'}
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Edit Dialog */}
      {editingNumber && (
        <Dialog open={!!editingNumber} onOpenChange={() => setEditingNumber(null)}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Edit Phone Number</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit(onEditPhoneNumber)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Select value={watchedCountry} onValueChange={(value) => setValue('country', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countryOptions.map((country) => (
                      <SelectItem key={country.code} value={country.code}>
                        {country.dialCode} {country.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phoneNumber">Phone Number</Label>
                <Input
                  id="phoneNumber"
                  placeholder="Enter phone number"
                  {...register('phoneNumber', { required: 'Phone number is required' })}
                />
                {errors.phoneNumber && (
                  <p className="text-sm text-red-600">{errors.phoneNumber.message}</p>
                )}
                
                {/* Real-time validation feedback */}
                {watchedPhone && validationResults.errors?.length > 0 && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      {validationResults.errors.join(', ')}
                    </AlertDescription>
                  </Alert>
                )}
                
                {watchedPhone && validationResults.isValid && (
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      Valid format: {validationResults.formatted}
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="label">Label (Optional)</Label>
                <Input
                  id="label"
                  placeholder="e.g., Customer Service, John Doe"
                  {...register('label')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Input
                  id="notes"
                  placeholder="Additional notes"
                  {...register('notes')}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setEditingNumber(null)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading || !validationResults.isValid}>
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  Update Number
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  )
}

export default PhoneNumberManager
