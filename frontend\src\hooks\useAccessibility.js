import { useEffect, useRef, useCallback, useState } from 'react'

/**
 * Hook for managing focus and keyboard navigation
 */
export const useFocusManagement = () => {
  const focusableElementsSelector = [
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    'a[href]',
    '[tabindex]:not([tabindex="-1"])',
    '[role="button"]:not([disabled])',
    '[role="link"]:not([disabled])',
    '[role="menuitem"]:not([disabled])',
    '[role="tab"]:not([disabled])',
  ].join(', ')

  const getFocusableElements = useCallback((container) => {
    if (!container) return []
    return Array.from(container.querySelectorAll(focusableElementsSelector))
  }, [])

  const trapFocus = useCallback((container) => {
    if (!container) return

    const focusableElements = getFocusableElements(container)
    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    const handleKeyDown = (e) => {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          e.preventDefault()
          lastElement?.focus()
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          e.preventDefault()
          firstElement?.focus()
        }
      }
    }

    container.addEventListener('keydown', handleKeyDown)
    
    // Focus first element
    firstElement?.focus()

    return () => {
      container.removeEventListener('keydown', handleKeyDown)
    }
  }, [getFocusableElements])

  const restoreFocus = useCallback((element) => {
    if (element && typeof element.focus === 'function') {
      element.focus()
    }
  }, [])

  return {
    getFocusableElements,
    trapFocus,
    restoreFocus,
  }
}

/**
 * Hook for managing ARIA announcements
 */
export const useAnnouncements = () => {
  const announcementRef = useRef(null)

  useEffect(() => {
    // Create announcement region if it doesn't exist
    if (!announcementRef.current) {
      const announcer = document.createElement('div')
      announcer.setAttribute('aria-live', 'polite')
      announcer.setAttribute('aria-atomic', 'true')
      announcer.setAttribute('class', 'sr-only')
      announcer.style.position = 'absolute'
      announcer.style.left = '-10000px'
      announcer.style.width = '1px'
      announcer.style.height = '1px'
      announcer.style.overflow = 'hidden'
      document.body.appendChild(announcer)
      announcementRef.current = announcer
    }

    return () => {
      if (announcementRef.current && document.body.contains(announcementRef.current)) {
        document.body.removeChild(announcementRef.current)
      }
    }
  }, [])

  const announce = useCallback((message, priority = 'polite') => {
    if (!announcementRef.current) return

    announcementRef.current.setAttribute('aria-live', priority)
    announcementRef.current.textContent = message

    // Clear after announcement
    setTimeout(() => {
      if (announcementRef.current) {
        announcementRef.current.textContent = ''
      }
    }, 1000)
  }, [])

  return { announce }
}

/**
 * Hook for keyboard navigation in lists/grids
 */
export const useKeyboardNavigation = (items = [], options = {}) => {
  const {
    orientation = 'vertical', // 'vertical', 'horizontal', 'grid'
    loop = true,
    onSelect,
    onEscape,
  } = options

  const [currentIndex, setCurrentIndex] = useState(-1)
  const containerRef = useRef(null)

  const handleKeyDown = useCallback((e) => {
    if (!items.length) return

    let newIndex = currentIndex

    switch (e.key) {
      case 'ArrowDown':
        if (orientation === 'vertical' || orientation === 'grid') {
          e.preventDefault()
          newIndex = currentIndex + 1
          if (newIndex >= items.length) {
            newIndex = loop ? 0 : items.length - 1
          }
        }
        break

      case 'ArrowUp':
        if (orientation === 'vertical' || orientation === 'grid') {
          e.preventDefault()
          newIndex = currentIndex - 1
          if (newIndex < 0) {
            newIndex = loop ? items.length - 1 : 0
          }
        }
        break

      case 'ArrowRight':
        if (orientation === 'horizontal' || orientation === 'grid') {
          e.preventDefault()
          newIndex = currentIndex + 1
          if (newIndex >= items.length) {
            newIndex = loop ? 0 : items.length - 1
          }
        }
        break

      case 'ArrowLeft':
        if (orientation === 'horizontal' || orientation === 'grid') {
          e.preventDefault()
          newIndex = currentIndex - 1
          if (newIndex < 0) {
            newIndex = loop ? items.length - 1 : 0
          }
        }
        break

      case 'Home':
        e.preventDefault()
        newIndex = 0
        break

      case 'End':
        e.preventDefault()
        newIndex = items.length - 1
        break

      case 'Enter':
      case ' ':
        e.preventDefault()
        if (currentIndex >= 0 && onSelect) {
          onSelect(items[currentIndex], currentIndex)
        }
        break

      case 'Escape':
        e.preventDefault()
        if (onEscape) {
          onEscape()
        }
        break

      default:
        return
    }

    if (newIndex !== currentIndex) {
      setCurrentIndex(newIndex)
    }
  }, [currentIndex, items, orientation, loop, onSelect, onEscape])

  useEffect(() => {
    const container = containerRef.current
    if (container) {
      container.addEventListener('keydown', handleKeyDown)
      return () => container.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])

  const focusItem = useCallback((index) => {
    if (containerRef.current && index >= 0 && index < items.length) {
      const focusableElements = containerRef.current.querySelectorAll('[role="option"], [role="menuitem"], [role="tab"], button, a')
      const element = focusableElements[index]
      if (element) {
        element.focus()
        setCurrentIndex(index)
      }
    }
  }, [items.length])

  return {
    containerRef,
    currentIndex,
    setCurrentIndex,
    focusItem,
    handleKeyDown,
  }
}

/**
 * Hook for managing reduced motion preferences
 */
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e) => setPrefersReducedMotion(e.matches)
    mediaQuery.addEventListener('change', handleChange)

    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersReducedMotion
}

/**
 * Hook for managing high contrast preferences
 */
export const useHighContrast = () => {
  const [prefersHighContrast, setPrefersHighContrast] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)')
    setPrefersHighContrast(mediaQuery.matches)

    const handleChange = (e) => setPrefersHighContrast(e.matches)
    mediaQuery.addEventListener('change', handleChange)

    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersHighContrast
}

/**
 * Hook for skip links functionality
 */
export const useSkipLinks = () => {
  const skipLinksRef = useRef([])

  const addSkipLink = useCallback((id, label) => {
    skipLinksRef.current.push({ id, label })
  }, [])

  const removeSkipLink = useCallback((id) => {
    skipLinksRef.current = skipLinksRef.current.filter(link => link.id !== id)
  }, [])

  const skipToContent = useCallback((targetId) => {
    const target = document.getElementById(targetId)
    if (target) {
      target.focus()
      target.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }, [])

  return {
    skipLinks: skipLinksRef.current,
    addSkipLink,
    removeSkipLink,
    skipToContent,
  }
}
