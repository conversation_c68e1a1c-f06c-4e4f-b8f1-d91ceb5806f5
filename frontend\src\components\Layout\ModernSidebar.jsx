import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import {
  LayoutDashboard,
  MessageSquare,
  Users,
  UsersRound,
  Calendar,
  Settings,
  ChevronLeft,
  ChevronRight,
  Phone,
} from 'lucide-react'

const menuItems = [
  {
    text: 'Dashboard',
    icon: LayoutDashboard,
    path: '/dashboard',
    description: 'Overview and analytics'
  },
  {
    text: 'Messages',
    icon: MessageSquare,
    path: '/messages',
    description: 'Send and manage messages'
  },
  {
    text: 'Contacts',
    icon: Users,
    path: '/contacts',
    description: 'Manage your contacts'
  },
  {
    text: 'Phone Numbers',
    icon: Phone,
    path: '/phone-numbers',
    description: 'Manage phone numbers'
  },
  {
    text: 'Groups',
    icon: UsersRound,
    path: '/groups',
    description: 'Manage group chats'
  },
  {
    text: 'Scheduled',
    icon: Calendar,
    path: '/scheduled',
    description: 'Scheduled messages'
  },
]

const bottomMenuItems = [
  { 
    text: 'Settings', 
    icon: Settings, 
    path: '/settings',
    description: 'Application settings'
  },
]

const ModernSidebar = ({ collapsed = false, onToggle }) => {
  const location = useLocation()
  const navigate = useNavigate()

  const handleNavigation = (path) => {
    navigate(path)
  }

  const isActive = (path) => {
    return location.pathname === path || 
           (path === '/dashboard' && location.pathname === '/')
  }

  const SidebarItem = ({ item, isBottom = false }) => {
    const Icon = item.icon
    const active = isActive(item.path)

    const button = (
      <Button
        variant={active ? "secondary" : "ghost"}
        className={cn(
          "w-full justify-start gap-3 h-12 px-3 transition-all duration-200 ease-in-out",
          collapsed ? "px-2" : "px-3",
          active && "bg-primary/10 text-primary border-r-2 border-primary",
          !active && "hover:bg-accent hover:text-accent-foreground",
          // Enhanced focus styles for accessibility
          "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        )}
        onClick={() => handleNavigation(item.path)}
        aria-label={collapsed ? `${item.text} - ${item.description}` : undefined}
        aria-current={active ? "page" : undefined}
        role="menuitem"
      >
        <Icon
          className={cn("h-5 w-5 flex-shrink-0", active && "text-primary")}
          aria-hidden="true"
        />
        {!collapsed && (
          <span className="font-medium text-sm">{item.text}</span>
        )}
      </Button>
    )

    if (collapsed) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {button}
            </TooltipTrigger>
            <TooltipContent side="right" className="ml-2">
              <p className="font-medium">{item.text}</p>
              <p className="text-xs text-muted-foreground">{item.description}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    return button
  }

  return (
    <div className={cn(
      "flex flex-col h-full bg-card border-r border-border transition-all duration-300 ease-in-out",
      collapsed ? "w-16" : "w-64"
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        {!collapsed && (
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <MessageSquare className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <h2 className="font-semibold text-sm">WhatsApp API</h2>
              <p className="text-xs text-muted-foreground">Gateway</p>
            </div>
          </div>
        )}
        
        {onToggle && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="h-8 w-8 p-0"
          >
            {collapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>

      {/* Navigation */}
      <div className="flex-1 flex flex-col justify-between p-2">
        <nav
          className="space-y-1"
          role="menu"
          aria-label="Main navigation menu"
        >
          {menuItems.map((item) => (
            <SidebarItem key={item.path} item={item} />
          ))}
        </nav>

        <div className="space-y-2">
          <Separator aria-hidden="true" />
          <nav
            role="menu"
            aria-label="Settings and preferences"
          >
            {bottomMenuItems.map((item) => (
              <SidebarItem key={item.path} item={item} isBottom />
            ))}
          </nav>
        </div>
      </div>
    </div>
  )
}

export default ModernSidebar
