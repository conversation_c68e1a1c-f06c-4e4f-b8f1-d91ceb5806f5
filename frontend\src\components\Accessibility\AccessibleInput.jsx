import React, { forwardRef, useId } from 'react'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Alert, AlertDescription } from '../ui/alert'
import { AlertCircle, CheckCircle2 } from 'lucide-react'
import { cn } from '@/lib/utils'

/**
 * Accessible Input component with enhanced ARIA support and validation
 */
const AccessibleInput = forwardRef(({
  label,
  description,
  error,
  success,
  required = false,
  showRequiredIndicator = true,
  className,
  containerClassName,
  ...props
}, ref) => {
  const inputId = useId()
  const descriptionId = useId()
  const errorId = useId()
  const successId = useId()

  const describedBy = [
    description ? descriptionId : null,
    error ? errorId : null,
    success ? successId : null,
  ].filter(Boolean).join(' ')

  return (
    <div className={cn("space-y-2", containerClassName)}>
      {/* Label */}
      {label && (
        <Label 
          htmlFor={inputId}
          className={cn(
            "text-sm font-medium",
            required && "after:content-['*'] after:ml-1 after:text-destructive"
          )}
        >
          {label}
          {required && showRequiredIndicator && (
            <span className="sr-only">(required)</span>
          )}
        </Label>
      )}

      {/* Description */}
      {description && (
        <p 
          id={descriptionId}
          className="text-sm text-muted-foreground"
        >
          {description}
        </p>
      )}

      {/* Input */}
      <Input
        ref={ref}
        id={inputId}
        required={required}
        aria-describedby={describedBy || undefined}
        aria-invalid={error ? 'true' : 'false'}
        className={cn(
          // Enhanced focus styles
          "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          // Error state
          error && "border-destructive focus:ring-destructive",
          // Success state
          success && "border-green-500 focus:ring-green-500",
          // High contrast support
          "contrast-more:border-2",
          className
        )}
        {...props}
      />

      {/* Error Message */}
      {error && (
        <Alert 
          id={errorId}
          className="border-destructive/50 text-destructive [&>svg]:text-destructive"
          role="alert"
          aria-live="polite"
        >
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Success Message */}
      {success && !error && (
        <Alert 
          id={successId}
          className="border-green-500/50 text-green-700 [&>svg]:text-green-600"
          role="status"
          aria-live="polite"
        >
          <CheckCircle2 className="h-4 w-4" />
          <AlertDescription>
            {success}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
})

AccessibleInput.displayName = "AccessibleInput"

export default AccessibleInput
