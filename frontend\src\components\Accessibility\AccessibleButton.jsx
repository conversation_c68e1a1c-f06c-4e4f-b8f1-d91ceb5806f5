import React, { forwardRef } from 'react'
import { Button } from '../ui/button'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

/**
 * Accessible Button component with enhanced ARIA support
 */
const AccessibleButton = forwardRef(({
  children,
  loading = false,
  loadingText = 'Loading...',
  ariaLabel,
  ariaDescribedBy,
  ariaExpanded,
  ariaPressed,
  ariaControls,
  ariaHaspopup,
  disabled,
  className,
  onClick,
  ...props
}, ref) => {
  const isDisabled = disabled || loading

  const handleClick = (e) => {
    if (isDisabled) {
      e.preventDefault()
      return
    }
    onClick?.(e)
  }

  const handleKeyDown = (e) => {
    // Ensure Enter and Space work consistently
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      if (!isDisabled) {
        onClick?.(e)
      }
    }
  }

  return (
    <Button
      ref={ref}
      disabled={isDisabled}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-expanded={ariaExpanded}
      aria-pressed={ariaPressed}
      aria-controls={ariaControls}
      aria-haspopup={ariaHaspopup}
      aria-busy={loading}
      className={cn(
        // Enhanced focus styles
        "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
        "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        // High contrast support
        "contrast-more:border-2 contrast-more:border-current",
        className
      )}
      {...props}
    >
      {loading && (
        <Loader2 
          className="mr-2 h-4 w-4 animate-spin" 
          aria-hidden="true"
        />
      )}
      <span aria-hidden={loading ? "true" : "false"}>
        {children}
      </span>
      {loading && (
        <span className="sr-only">
          {loadingText}
        </span>
      )}
    </Button>
  )
})

AccessibleButton.displayName = "AccessibleButton"

export default AccessibleButton
